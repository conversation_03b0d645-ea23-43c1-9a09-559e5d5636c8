import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { PosPage } from './pages/PosPage';
import { OrderManagementPage } from './pages/OrderManagementPage';
import { LoginPage } from './pages/LoginPage';
import { ProfilePage } from './pages/ProfilePage';
import { Header } from './components/Header';
// import { Footer } from './components/Footer';
import TableOverlayManager from './pages/TableOverlayManagerPage';
import LayoutViewerTabPage from './pages/LayoutViewerTabsPage';
import './App.css';

export function App() {
  const [user, setUser] = useState<{ username: string; role: string } | null>(null);

  const handleLogin = (user: { username: string; role: string }) => {
    setUser(user);
  };

  const handleLogout = () => {
    setUser(null);
  };

  return (
    <Router>
      <div className="flex flex-col h-screen bg-gray-100">
        <Header user={user} onLogout={handleLogout} />
        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <Routes>
            <Route path="/login" element={<LoginPage onLogin={handleLogin} />} />
            <Route path="/" element={user ? <PosPage /> : <Navigate to="/login" />} />
            <Route path="/orders" element={user ? <OrderManagementPage /> : <Navigate to="/login" />} />
            <Route path="/profile" element={user ? <ProfilePage user={user} /> : <Navigate to="/login" />} />
            <Route path="/table-manager" element={user ? <TableOverlayManager /> : <Navigate to="/login" />} />
            <Route path="/table-viewer" element={user ? <LayoutViewerTabPage /> : <Navigate to="/login" />} />
          </Routes>
        </main>
        {/* <Footer /> */}
      </div>
    </Router>
  );
}