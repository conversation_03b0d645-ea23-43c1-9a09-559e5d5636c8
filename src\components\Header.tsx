import React from 'react';
import { Link } from 'react-router-dom';

export function Header({ user, onLogout }: { user: { username: string; role: string } | null; onLogout: () => void }) {
  return (
    <header className="bg-blue-600 text-white p-4 shadow-md">
      <div className="container mx-auto flex justify-between items-center">
        <h1 className="text-2xl font-bold">POS System</h1>
        <nav className="flex space-x-4">
          {user ? (
            <>
              <Link to="/" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
                POS
              </Link>
              {user.role === 'admin' && (
                <Link to="/orders" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
                  Order Management
                </Link>
              )}
              <Link to="/table-manager" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
                Table Manager
              </Link>
              <Link to="/table-viewer" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
                Table Viewer
              </Link>
              <Link to="/profile" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
                Profile
              </Link>
              <button onClick={onLogout} className="bg-red-500 px-4 py-2 rounded-md hover:bg-red-600">
                Logout
              </button>
            </>
          ) : (
            <Link to="/login" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
              Login
            </Link>
          )}
        </nav>
      </div>
    </header>
  );
}