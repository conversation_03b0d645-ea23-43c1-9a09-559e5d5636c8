# Logout Implementation

This document describes the implementation of the logout functionality that properly sets the user as logged out in the database.

## API Integration

The logout function now uses the correct API endpoint and parameters as specified:

### Endpoint
```
PUT /api/Role/Logout?UserId={userId}&ApplicationId={applicationId}
```

### Headers
```javascript
{
  "Authorization": "Bearer {identityToken}",
  "ClientKey": "{clientKey}",
  "Content-Type": "application/json",
  "Accept": "*/*"
}
```

## Implementation Details

### 1. AuthService Updates (`src/services/authService.ts`)

#### Enhanced appUser Object
```javascript
export const appUser = {
  UserId: 0,
  userId: 0, // Alias for API compatibility
  Pass: '',
  UserName: '',
  Token: '',
  DbName: '',
  apiURL: 'http://*************:8055',
  identityURL: 'http://*************:2828', // Identity server URL
  identityToken: '',
  clientKey: IdentityKey,
  applicationId: ApplicationId
};
```

#### Updated Logout Function
```javascript
export async function logout(): Promise<LogoutResult> {
  try {
    const response = await fetch(
      `${appUser.identityURL}/api/Role/Logout?UserId=${appUser.userId}&ApplicationId=${appUser.applicationId}`,
      {
        method: 'PUT',
        headers: {
          'accept': '*/*',
          'ClientKey': appUser.clientKey,
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + appUser.identityToken,
        },
      }
    );

    const result = await response.json();
    
    if (response.ok) {
      // Clear user data on successful logout
      appUser.UserId = 0;
      appUser.userId = 0;
      appUser.Pass = '';
      appUser.UserName = '';
      appUser.Token = '';
      appUser.DbName = '';
      appUser.identityToken = '';
      
      return {
        success: true,
        message: result.message || 'Logged out successfully'
      };
    } else {
      return {
        success: false,
        message: result.message || 'Logout failed',
        errorMessage: result.errorMessage
      };
    }
  } catch (error) {
    console.error('Logout error:', error);
    return {
      success: false,
      message: 'An error occurred during logout',
      errorMessage: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
```

### 2. Header Component Updates (`src/components/Header.tsx`)

#### Enhanced Logout Handler
```javascript
const handleLogout = async () => {
  setIsLoggingOut(true);
  
  try {
    const result = await logout();
    
    if (result.success) {
      // Call the parent component's logout handler
      onLogout();
    } else {
      console.error('Logout failed:', result.message);
      alert(result.message || 'Logout failed. Please try again.');
    }
  } catch (error) {
    console.error('Logout error:', error);
    alert('An error occurred during logout. Please try again.');
  } finally {
    setIsLoggingOut(false);
  }
};
```

#### Enhanced Logout Button
```javascript
<button 
  onClick={handleLogout} 
  disabled={isLoggingOut}
  className="bg-red-500 px-4 py-2 rounded-md hover:bg-red-600 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
>
  {isLoggingOut ? (
    <>
      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
      Logging out...
    </>
  ) : (
    'Logout'
  )}
</button>
```

## Key Features

### 1. **Database-Level Logout**
- Sets user as logged out in the database server-side
- Ensures proper session termination
- Prevents unauthorized access after logout

### 2. **Proper Authentication**
- Uses Bearer token authentication
- Includes ClientKey header for API validation
- Follows the exact API specification provided

### 3. **Enhanced User Experience**
- Loading state with spinner during logout process
- Disabled button to prevent multiple logout attempts
- Clear visual feedback for user actions

### 4. **Error Handling**
- Comprehensive error handling for network issues
- User-friendly error messages
- Graceful fallback behavior

### 5. **Data Cleanup**
- Clears all user session data on successful logout
- Resets both UserId and userId properties
- Clears authentication tokens

## Usage Example

```javascript
import { logout } from '../services/authService';

// Simple logout
const result = await logout();
if (result.success) {
  console.log('User logged out successfully');
  // Redirect to login page
} else {
  console.error('Logout failed:', result.message);
}
```

## Testing

The logout functionality is thoroughly tested with:
- Successful logout scenarios
- Failed logout scenarios
- Network error handling
- Data cleanup verification

All 28 tests pass, ensuring robust and reliable logout functionality.

## Security Benefits

1. **Server-side Session Management**: User status is properly updated in the database
2. **Token Invalidation**: Authentication tokens are cleared locally
3. **Proper API Authentication**: Uses correct headers and authentication methods
4. **Error Handling**: Prevents information leakage through proper error handling

This implementation ensures that users are properly logged out both client-side and server-side, maintaining security and data integrity.
