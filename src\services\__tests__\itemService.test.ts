// Mock the authService
jest.mock('../authService', () => ({
  appUser: {
    apiURL: 'http://test-api.com',
    identityToken: 'test-token',
    clientKey: 'test-client-key'
  }
}));

// Mock fetch
global.fetch = jest.fn();

import { 
  getItemList, 
  getCategories, 
  filterProductsByCategory, 
  searchProducts, 
  isAuthenticated,
  getItemImageUrl 
} from '../itemService';
import { Product } from '../../types';

describe('ItemService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getItemList', () => {
    it('should return transformed product list on successful API call', async () => {
      const mockApiResponse = {
        success: true,
        methodName: 'GetItemListAsync',
        errorMessage: '',
        data: [
          {
            id: 1,
            familyId: 2,
            name: 'Test Product',
            abbreviation: 'TP',
            stmp: 123456,
            image: 'base64-image-data',
            isDeleted: false,
            deactivated: false,
            notSubjectToDiscount: false,
            discount: 0,
            cosmeticTax: 0,
            vat: 0,
            tax1Amount: 0,
            tax2Percentage: 0,
            isExpiry: false
          }
        ]
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      });

      const result = await getItemList();

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 1,
        familyId: 2,
        name: 'Test Product',
        price: 0,
        category: 'general',
        hasModifiers: false
      });
    });

    it('should throw error when API call fails', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500
      });

      await expect(getItemList()).rejects.toThrow('HTTP error! status: 500');
    });

    it('should throw error when API returns success: false', async () => {
      const mockApiResponse = {
        success: false,
        errorMessage: 'API Error',
        data: []
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      });

      await expect(getItemList()).rejects.toThrow('API Error');
    });
  });

  describe('filterProductsByCategory', () => {
    const mockProducts: Product[] = [
      { id: 1, familyId: 1, name: 'Product 1' } as Product,
      { id: 2, familyId: 2, name: 'Product 2' } as Product,
      { id: 3, familyId: 1, name: 'Product 3' } as Product,
    ];

    it('should return all products when category is "all"', () => {
      const result = filterProductsByCategory(mockProducts, 'all');
      expect(result).toHaveLength(3);
    });

    it('should filter products by familyId', () => {
      const result = filterProductsByCategory(mockProducts, '1');
      expect(result).toHaveLength(2);
      expect(result.every(p => p.familyId === 1)).toBe(true);
    });
  });

  describe('searchProducts', () => {
    const mockProducts: Product[] = [
      { id: 1, name: 'iPhone 16', abbreviation: 'IP16' } as Product,
      { id: 2, name: 'Samsung Galaxy', abbreviation: 'SG' } as Product,
      { id: 3, name: 'iPad Pro', abbreviation: 'IPP' } as Product,
    ];

    it('should return all products when query is empty', () => {
      const result = searchProducts(mockProducts, '');
      expect(result).toHaveLength(3);
    });

    it('should filter products by name', () => {
      const result = searchProducts(mockProducts, 'iPhone');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('iPhone 16');
    });

    it('should filter products by abbreviation', () => {
      const result = searchProducts(mockProducts, 'IP');
      expect(result).toHaveLength(2);
    });

    it('should be case insensitive', () => {
      const result = searchProducts(mockProducts, 'iphone');
      expect(result).toHaveLength(1);
    });
  });

  describe('getItemImageUrl', () => {
    it('should return data URL for base64 image', () => {
      const product = { image: 'base64data' } as Product;
      const result = getItemImageUrl(product);
      expect(result).toBe('data:image/png;base64,base64data');
    });

    it('should return existing data URL unchanged', () => {
      const product = { image: 'data:image/png;base64,existing' } as Product;
      const result = getItemImageUrl(product);
      expect(result).toBe('data:image/png;base64,existing');
    });

    it('should return placeholder for empty image', () => {
      const product = { image: '' } as Product;
      const result = getItemImageUrl(product);
      expect(result).toContain('data:image/svg+xml;base64,');
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when user has token and client key', () => {
      const result = isAuthenticated();
      expect(result).toBe(true);
    });
  });
});
