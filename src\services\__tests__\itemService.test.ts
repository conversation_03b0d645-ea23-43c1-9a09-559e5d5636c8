// Mock the authService
jest.mock('../authService', () => ({
  appUser: {
    apiURL: 'http://test-api.com',
    identityToken: 'test-token',
    clientKey: 'test-client-key'
  }
}));

// Mock fetch
global.fetch = jest.fn();

import {
  getItemList,
  getFamilyList,
  getItemUnitList,
  getUnitPriceList,
  getCurrencyList,
  getEnrichedProducts,
  formatPrice,
  getProductPrice,
  filterProductsByCategory,
  searchProducts,
  isAuthenticated,
  getItemImageUrl
} from '../itemService';
import { Product } from '../../types';

describe('ItemService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getItemList', () => {
    it('should return transformed product list on successful API call', async () => {
      const mockApiResponse = {
        success: true,
        methodName: 'GetItemListAsync',
        errorMessage: '',
        data: [
          {
            id: 1,
            familyId: 2,
            name: 'Test Product',
            abbreviation: 'TP',
            stmp: 123456,
            image: 'base64-image-data',
            isDeleted: false,
            deactivated: false,
            notSubjectToDiscount: false,
            discount: 0,
            cosmeticTax: 0,
            vat: 0,
            tax1Amount: 0,
            tax2Percentage: 0,
            isExpiry: false
          }
        ]
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      });

      const result = await getItemList();

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 1,
        familyId: 2,
        name: 'Test Product',
        price: 0,
        category: '2', // familyId as string
        hasModifiers: false
      });
    });

    it('should throw error when API call fails', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500
      });

      await expect(getItemList()).rejects.toThrow('HTTP error! status: 500');
    });

    it('should throw error when API returns success: false', async () => {
      const mockApiResponse = {
        success: false,
        errorMessage: 'API Error',
        data: []
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      });

      await expect(getItemList()).rejects.toThrow('API Error');
    });
  });

  describe('filterProductsByCategory', () => {
    const mockProducts: Product[] = [
      { id: 1, familyId: 1, name: 'Product 1' } as Product,
      { id: 2, familyId: 2, name: 'Product 2' } as Product,
      { id: 3, familyId: 1, name: 'Product 3' } as Product,
    ];

    it('should return all products when category is "all"', () => {
      const result = filterProductsByCategory(mockProducts, 'all');
      expect(result).toHaveLength(3);
    });

    it('should filter products by familyId', () => {
      const result = filterProductsByCategory(mockProducts, '1');
      expect(result).toHaveLength(2);
      expect(result.every(p => p.familyId === 1)).toBe(true);
    });
  });

  describe('searchProducts', () => {
    const mockProducts: Product[] = [
      { id: 1, name: 'iPhone 16', abbreviation: 'IP16' } as Product,
      { id: 2, name: 'Samsung Galaxy', abbreviation: 'SG' } as Product,
      { id: 3, name: 'iPad Pro', abbreviation: 'IPP' } as Product,
    ];

    it('should return all products when query is empty', () => {
      const result = searchProducts(mockProducts, '');
      expect(result).toHaveLength(3);
    });

    it('should filter products by name', () => {
      const result = searchProducts(mockProducts, 'iPhone');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('iPhone 16');
    });

    it('should filter products by abbreviation', () => {
      const result = searchProducts(mockProducts, 'IP');
      expect(result).toHaveLength(2);
    });

    it('should be case insensitive', () => {
      const result = searchProducts(mockProducts, 'iphone');
      expect(result).toHaveLength(1);
    });
  });

  describe('getItemImageUrl', () => {
    it('should return data URL for base64 image', () => {
      const product = { image: 'base64data' } as Product;
      const result = getItemImageUrl(product);
      expect(result).toBe('data:image/png;base64,base64data');
    });

    it('should return existing data URL unchanged', () => {
      const product = { image: 'data:image/png;base64,existing' } as Product;
      const result = getItemImageUrl(product);
      expect(result).toBe('data:image/png;base64,existing');
    });

    it('should return placeholder for empty image', () => {
      const product = { image: '' } as Product;
      const result = getItemImageUrl(product);
      expect(result).toContain('data:image/svg+xml;base64,');
    });
  });

  describe('getFamilyList', () => {
    it('should return family list on successful API call', async () => {
      const mockApiResponse = {
        success: true,
        methodName: 'GetFamilyListAsync',
        errorMessage: '',
        data: [
          {
            id: 2,
            name: 'APPLE',
            parentId: 1,
            stmp: 630196,
            isDeleted: false
          },
          {
            id: 3,
            name: 'SAMSUNG',
            parentId: 1,
            stmp: 630197,
            isDeleted: false
          }
        ]
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      });

      const result = await getFamilyList();

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        id: 2,
        name: 'APPLE',
        parentId: 1,
        isDeleted: false
      });
    });

    it('should filter out deleted families', async () => {
      const mockApiResponse = {
        success: true,
        methodName: 'GetFamilyListAsync',
        errorMessage: '',
        data: [
          {
            id: 2,
            name: 'APPLE',
            parentId: 1,
            stmp: 630196,
            isDeleted: false
          },
          {
            id: 3,
            name: 'DELETED_FAMILY',
            parentId: 1,
            stmp: 630197,
            isDeleted: true
          }
        ]
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      });

      const result = await getFamilyList();

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('APPLE');
    });
  });

  // Remove the old getProductsWithFamilies test since it's now a wrapper

  describe('getItemUnitList', () => {
    it('should return item units list on successful API call', async () => {
      const mockApiResponse = {
        success: true,
        methodName: 'GetItemUnitListAsync',
        errorMessage: '',
        data: [
          {
            id: 1,
            itemId: 1,
            unitId: 38,
            symbol: 'Piece',
            barcode: '231564897543',
            isBasicUnit: true,
            isDefaultUnit: true,
            stmp: 661227,
            isDeleted: false,
            formula: 1,
            image: null
          }
        ]
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      });

      const result = await getItemUnitList();

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 1,
        itemId: 1,
        unitId: 38,
        symbol: 'Piece',
        barcode: '231564897543',
        isBasicUnit: true,
        isDefaultUnit: true,
        stmp: 661227,
        isDeleted: false,
        formula: 1
      });
    });
  });

  describe('getUnitPriceList', () => {
    it('should return unit prices list on successful API call', async () => {
      const mockApiResponse = {
        success: true,
        methodName: 'GetUnitPriceListAsync',
        errorMessage: '',
        data: [
          {
            id: 1,
            itemUnitId: 1,
            price: 76500000,
            price2: 850,
            stmp: 661218,
            priceListId: 1
          }
        ]
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      });

      const result = await getUnitPriceList();

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 1,
        itemUnitId: 1,
        price: 76500000,
        price2: 850,
        stmp: 661218,
        priceListId: 1
      });
    });
  });

  describe('getCurrencyList', () => {
    it('should return currencies list on successful API call', async () => {
      const mockApiResponse = {
        success: true,
        methodName: 'GetCurrencyListAsync',
        errorMessage: '',
        data: [
          {
            id: 1,
            name: 'Lebanese Pound',
            nameAr: null,
            nameFr: null,
            symbol: 'LBP',
            isDeleted: false,
            stmp: 566071
          }
        ]
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      });

      const result = await getCurrencyList();

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 1,
        name: 'Lebanese Pound',
        nameAr: null,
        nameFr: null,
        symbol: 'LBP',
        isDeleted: false,
        stmp: 566071
      });
    });
  });

  describe('formatPrice', () => {
    it('should format price without currency', () => {
      const result = formatPrice(1000);
      expect(result).toBe('1000.00');
    });

    it('should format price with currency', () => {
      const currency = { id: 1, name: 'US Dollar', symbol: 'USD', isDeleted: false, stmp: 1, nameAr: null, nameFr: null };
      const result = formatPrice(1000, currency);
      expect(result).toBe('1,000.00 USD');
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when user has token and client key', () => {
      const result = isAuthenticated();
      expect(result).toBe(true);
    });
  });
});
