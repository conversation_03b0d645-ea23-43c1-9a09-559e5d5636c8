export interface Modifier {
  id: string;
  name: string;
  price: number;
}

export interface Product {
  id: number;
  familyId: number;
  name: string;
  abbreviation: string;
  stmp: number;
  image: string;
  isDeleted: boolean;
  deactivated: boolean;
  notSubjectToDiscount: boolean;
  discount: number;
  cosmeticTax: number;
  vat: number;
  tax1Amount: number;
  tax2Percentage: number;
  isExpiry: boolean;
  // Additional properties for UI compatibility
  price?: number;
  category?: string;
  hasModifiers?: boolean;
  modifiers?: Modifier[];
}

export interface Category {
  id: string;
  name: string;
}

export interface OrderItem extends Product {
  quantity: number;
  modifiers?: Modifier[];
  modifierPrice?: number;
}

export interface CustomerInfo {
  name: string;
  phone: string;
}

export interface OrderSummaryData {
  items: OrderItem[];
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  orderType: 'dine-in' | 'takeout' | 'delivery';
  customer?: CustomerInfo;
}

export type OrderType = 'dine-in' | 'takeout' | 'delivery';