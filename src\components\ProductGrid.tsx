import React from 'react';
import { Product } from '../types';
import { getItemImageUrl } from '../services/itemService';

interface ProductGridProps {
  products: Product[];
  onProductSelect: (product: Product) => void;
}

export function ProductGrid({ products, onProductSelect }: ProductGridProps) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
      {products.map(product => (
        <button
          key={product.id}
          className="bg-white border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow flex flex-col items-center text-center"
          onClick={() => onProductSelect(product)}
          disabled={product.deactivated || product.isDeleted}
        >
          <div className="w-full h-32 mb-2 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
            {product.image ? (
              <img
                src={getItemImageUrl(product)}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Fallback to placeholder if image fails to load
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling?.classList.remove('hidden');
                }}
              />
            ) : null}
            <span className={`text-gray-400 text-2xl font-bold ${product.image ? 'hidden' : ''}`}>
              {product.name.charAt(0)}
            </span>
          </div>
          <h3 className="font-medium text-sm line-clamp-2" title={product.name}>
            {product.name}
          </h3>
          <p className="text-xs text-gray-500 mb-1">{product.abbreviation}</p>
          <p className="text-gray-700 font-semibold">
            {product.price ? `$${product.price.toFixed(2)}` : 'Price TBD'}
          </p>
          {product.deactivated && (
            <span className="text-xs text-red-500 mt-1">Deactivated</span>
          )}
        </button>
      ))}
    </div>
  );
}