import React from 'react';
export function ProductGrid({
  products,
  onProductSelect
}) {
  return <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
      {products.map(product => <button key={product.id} className="bg-white border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow flex flex-col items-center text-center" onClick={() => onProductSelect(product)}>
          <div className="w-full h-32 mb-2 bg-gray-100 rounded flex items-center justify-center overflow-hidden" style={{
        backgroundImage: product.image ? `url(${product.image})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}>
            {!product.image && <span className="text-gray-400">{product.name.charAt(0)}</span>}
          </div>
          <h3 className="font-medium">{product.name}</h3>
          <p className="text-gray-700">${product.price.toFixed(2)}</p>
        </button>)}
    </div>;
}