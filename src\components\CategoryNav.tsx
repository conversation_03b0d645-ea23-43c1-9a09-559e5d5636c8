import React from 'react';
import { Category } from '../types';

interface CategoryNavProps {
  categories: Category[];
  selectedCategory: string;
  onSelectCategory: (categoryId: string) => void;
}

export function CategoryNav({
  categories,
  selectedCategory,
  onSelectCategory
}: CategoryNavProps) {
  return (
    <div className="mt-4">
      <h3 className="text-sm font-medium text-gray-700 mb-2">Product Families</h3>
      <div className="flex overflow-x-auto pb-2 space-x-2">
        <button
          className={`px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
            selectedCategory === 'all'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
          onClick={() => onSelectCategory('all')}
        >
          All Items
        </button>
        {categories
          .filter(category => category.id !== 'all') // Filter out 'all' since we handle it separately
          .map(category => (
            <button
              key={category.id}
              className={`px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              onClick={() => onSelectCategory(category.id)}
            >
              {category.name}
            </button>
          ))}
      </div>
    </div>
  );
}