import React from 'react';
export function CategoryNav({
  categories,
  selectedCategory,
  onSelectCategory
}) {
  return <div className="mt-4">
      <div className="flex overflow-x-auto pb-2 space-x-2">
        <button className={`px-4 py-2 rounded-lg whitespace-nowrap ${selectedCategory === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`} onClick={() => onSelectCategory('all')}>
          All Items
        </button>
        {categories.map(category => <button key={category.id} className={`px-4 py-2 rounded-lg whitespace-nowrap ${selectedCategory === category.id ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`} onClick={() => onSelectCategory(category.id)}>
            {category.name}
          </button>)}
      </div>
    </div>;
}