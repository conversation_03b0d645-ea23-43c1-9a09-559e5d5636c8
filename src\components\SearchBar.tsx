import React, { useState } from 'react';
import { SearchIcon, XIcon } from 'lucide-react';
export function SearchBar({
  onSearch
}) {
  const [query, setQuery] = useState('');
  const handleSearch = e => {
    setQuery(e.target.value);
    onSearch(e.target.value);
  };
  const clearSearch = () => {
    setQuery('');
    onSearch('');
  };
  return <div className="relative">
      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <SearchIcon size={18} className="text-gray-400" />
      </div>
      <input type="text" value={query} onChange={handleSearch} className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Search products..." />
      {query && <button onClick={clearSearch} className="absolute inset-y-0 right-0 flex items-center pr-3">
          <XIcon size={18} className="text-gray-400 hover:text-gray-600" />
        </button>}
    </div>;
}