import { dbService } from './indexedDBService';
import { Table, Order, OrderItem } from '../types';

// Initialize table service
export async function initializeTableService(): Promise<void> {
  await dbService.init();
}

// Get all tables
export async function getAllTables(): Promise<Table[]> {
  await initializeTableService();
  return await dbService.getAll<Table>('tables');
}

// Get table by ID
export async function getTableById(tableId: number): Promise<Table | null> {
  await initializeTableService();
  return await dbService.get<Table>('tables', tableId);
}

// Save table
export async function saveTable(table: Table): Promise<void> {
  await initializeTableService();
  await dbService.put('tables', table);
}

// Save multiple tables
export async function saveTables(tables: Table[]): Promise<void> {
  await initializeTableService();
  await dbService.bulkPut('tables', tables);
}

// Update table status
export async function updateTableStatus(
  tableId: number, 
  status: Table['status'], 
  orderId?: string
): Promise<void> {
  await initializeTableService();
  
  const table = await dbService.get<Table>('tables', tableId);
  if (!table) {
    throw new Error('Table not found');
  }

  const updatedTable: Table = {
    ...table,
    status,
    currentOrderId: orderId,
    lastOrderTime: orderId ? Date.now() : table.lastOrderTime
  };

  await dbService.put('tables', updatedTable);
}

// Get table's current order
export async function getTableCurrentOrder(tableId: number): Promise<Order | null> {
  await initializeTableService();
  
  const table = await dbService.get<Table>('tables', tableId);
  if (!table || !table.currentOrderId) {
    return null;
  }

  return await dbService.get<Order>('orders', table.currentOrderId);
}

// Link order to table (for dine-in)
export async function linkOrderToTable(orderId: string, tableId: number): Promise<void> {
  await initializeTableService();
  
  // Update table with current order
  await updateTableStatus(tableId, 'occupied', orderId);
  
  // Update order with table information
  const order = await dbService.get<Order>('orders', orderId);
  if (order) {
    const table = await dbService.get<Table>('tables', tableId);
    const updatedOrder: Order = {
      ...order,
      tableId,
      tableName: table ? `Table ${table.number}` : `Table ${tableId}`
    };
    await dbService.put('orders', updatedOrder);
  }
}

// Clear table (when order is completed/cancelled)
export async function clearTable(tableId: number): Promise<void> {
  await initializeTableService();
  
  const table = await dbService.get<Table>('tables', tableId);
  if (table) {
    const updatedTable: Table = {
      ...table,
      status: 'available',
      currentOrderId: undefined
    };
    await dbService.put('tables', updatedTable);
  }
}

// Get available tables
export async function getAvailableTables(): Promise<Table[]> {
  await initializeTableService();
  return await dbService.getByIndex<Table>('tables', 'status', 'available');
}

// Get occupied tables
export async function getOccupiedTables(): Promise<Table[]> {
  await initializeTableService();
  return await dbService.getByIndex<Table>('tables', 'status', 'occupied');
}

// Auto-save order to table (for dine-in orders)
export async function autoSaveOrderToTable(
  tableId: number,
  items: OrderItem[],
  customerInfo: any,
  discount: number = 0,
  discountType: 'percentage' | 'fixed' = 'percentage'
): Promise<string> {
  await initializeTableService();
  
  // Check if table already has an order
  const existingOrder = await getTableCurrentOrder(tableId);
  
  if (existingOrder) {
    // Update existing order
    const updatedOrder: Order = {
      ...existingOrder,
      items: [...items], // Replace items with current cart
      customerInfo,
      updatedAt: Date.now()
    };
    
    await dbService.put('orders', updatedOrder);
    return existingOrder.id;
  } else {
    // Create new draft order
    const orderId = Date.now().toString(36) + Math.random().toString(36).substr(2);
    const now = Date.now();
    const date = new Date(now);
    
    const newOrder: Order = {
      id: orderId,
      orderNumber: `DRAFT-${tableId}-${Date.now()}`,
      invoiceId: '', // Will be set during checkout
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString(),
      orderType: 'dine-in',
      customerInfo,
      items: [...items],
      total: 0, // Will be calculated during checkout
      status: 'pending',
      createdBy: 'Auto-save',
      createdAt: now,
      updatedAt: now,
      tableId,
      tableName: `Table ${tableId}`
    };
    
    await dbService.put('orders', newOrder);
    await linkOrderToTable(orderId, tableId);
    
    return orderId;
  }
}

// Load order from table
export async function loadOrderFromTable(tableId: number): Promise<{
  order: Order | null;
  items: OrderItem[];
}> {
  await initializeTableService();
  
  const order = await getTableCurrentOrder(tableId);
  
  if (order) {
    return {
      order,
      items: order.items || []
    };
  }
  
  return {
    order: null,
    items: []
  };
}

// Get table statistics
export async function getTableStatistics(): Promise<{
  totalTables: number;
  availableTables: number;
  occupiedTables: number;
  reservedTables: number;
  occupancyRate: number;
}> {
  await initializeTableService();
  
  const tables = await getAllTables();
  const available = tables.filter(t => t.status === 'available').length;
  const occupied = tables.filter(t => t.status === 'occupied').length;
  const reserved = tables.filter(t => t.status === 'reserved').length;
  
  return {
    totalTables: tables.length,
    availableTables: available,
    occupiedTables: occupied,
    reservedTables: reserved,
    occupancyRate: tables.length > 0 ? (occupied / tables.length) * 100 : 0
  };
}

// Initialize default tables if none exist
export async function initializeDefaultTables(): Promise<void> {
  await initializeTableService();
  
  const existingTables = await getAllTables();
  if (existingTables.length === 0) {
    const defaultTables: Table[] = [
      { id: 1, x: 100, y: 100, width: 80, height: 80, number: 1, seats: 4, status: 'available', borderColor: '#16a34a' },
      { id: 2, x: 200, y: 100, width: 80, height: 80, number: 2, seats: 4, status: 'available', borderColor: '#16a34a' },
      { id: 3, x: 300, y: 100, width: 80, height: 80, number: 3, seats: 4, status: 'available', borderColor: '#16a34a' },
      { id: 4, x: 400, y: 100, width: 80, height: 80, number: 4, seats: 4, status: 'available', borderColor: '#16a34a' },
      { id: 5, x: 100, y: 200, width: 120, height: 80, number: 5, seats: 6, status: 'available', borderColor: '#16a34a' },
      { id: 6, x: 250, y: 200, width: 120, height: 80, number: 6, seats: 6, status: 'available', borderColor: '#16a34a' },
    ];
    
    await saveTables(defaultTables);
  }
}
