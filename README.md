# Restaurant POS System

A modern React-based Point of Sale system for restaurants with secure two-step authentication.

This code was generated by [Magic Patterns](https://magicpatterns.com) for this design: [Source Design](https://www.magicpatterns.com/c/9ziczyjbtaxnvhcuqduchd)

## Features

- **Two-Step Authentication**: Secure login process with RSA encryption
- **Database Selection**: Support for multiple databases per user
- **Modern UI**: Built with React, TypeScript, and Tailwind CSS
- **Responsive Design**: Works on desktop and mobile devices
- **Testing**: Comprehensive test suite with Jest

## Authentication Flow

The application implements a two-step authentication process:

### Step 1: Initial Login (LoginRSA)
- User enters username and password
- Password is encrypted using RSA public key
- API call to `/api/Login/LoginRSA` endpoint
- Returns user information and available databases

### Step 2: Token Generation (GenerateTokenRSA)
- If multiple databases are available, user selects one
- If only one database, it's selected automatically
- API call to `/api/Login/GenerateTokenRSA` endpoint
- Returns JWT token for authenticated session

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
REACT_APP_APPLICATION_ID=your-application-id-here
REACT_APP_IDENTITY_KEY=your-identity-key-here
```

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Set up environment variables (see above)
4. Start the development server:
   ```bash
   npm run dev
   ```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run lint` - Run ESLint

## API Configuration

The authentication service is configured to use:
- **Base URL**: `http://*************:2828`
- **LoginRSA Endpoint**: `/api/Login/LoginRSA`
- **GenerateTokenRSA Endpoint**: `/api/Login/GenerateTokenRSA`

## Security

- Passwords are encrypted using RSA encryption before transmission
- JWT tokens are used for session management
- Environment variables are used for sensitive configuration

## Testing

Run the test suite:

```bash
npm test
```

Tests cover:
- Successful login scenarios
- Failed login scenarios
- Token generation
- Database selection logic
