import { syncData } from './itemService';
import { dbService } from './indexedDBService';

// Auto-sync configuration
const AUTO_SYNC_INTERVAL = 15 * 60 * 1000; // 15 minutes in milliseconds
const SYNC_STATUS_KEY = 'autoSyncStatus';

// Auto-sync status interface
interface AutoSyncStatus {
  isEnabled: boolean;
  lastSyncTime: number;
  nextSyncTime: number;
  syncCount: number;
  lastError?: string;
}

// Global auto-sync state
let autoSyncInterval: NodeJS.Timeout | null = null;
let isAutoSyncRunning = false;

// Get auto-sync status
export async function getAutoSyncStatus(): Promise<AutoSyncStatus> {
  try {
    await dbService.init();
    const status = await dbService.get<AutoSyncStatus>('metadata', SYNC_STATUS_KEY);
    
    if (status) {
      return status;
    }
    
    // Default status
    const defaultStatus: AutoSyncStatus = {
      isEnabled: true,
      lastSyncTime: 0,
      nextSyncTime: Date.now() + AUTO_SYNC_INTERVAL,
      syncCount: 0
    };
    
    await dbService.put('metadata', { key: SYNC_STATUS_KEY, ...defaultStatus });
    return defaultStatus;
  } catch (error) {
    console.error('Error getting auto-sync status:', error);
    return {
      isEnabled: false,
      lastSyncTime: 0,
      nextSyncTime: 0,
      syncCount: 0,
      lastError: 'Failed to get sync status'
    };
  }
}

// Update auto-sync status
async function updateAutoSyncStatus(updates: Partial<AutoSyncStatus>): Promise<void> {
  try {
    await dbService.init();
    const currentStatus = await getAutoSyncStatus();
    const newStatus = { ...currentStatus, ...updates };
    await dbService.put('metadata', { key: SYNC_STATUS_KEY, ...newStatus });
  } catch (error) {
    console.error('Error updating auto-sync status:', error);
  }
}

// Perform sync operation
async function performSync(): Promise<boolean> {
  try {
    console.log('Auto-sync: Starting data synchronization...');
    
    const startTime = Date.now();
    await syncData(); // Use existing sync function from itemService
    const endTime = Date.now();
    
    console.log(`Auto-sync: Completed in ${endTime - startTime}ms`);
    
    // Update sync status
    await updateAutoSyncStatus({
      lastSyncTime: endTime,
      nextSyncTime: endTime + AUTO_SYNC_INTERVAL,
      syncCount: (await getAutoSyncStatus()).syncCount + 1,
      lastError: undefined
    });
    
    return true;
  } catch (error) {
    console.error('Auto-sync: Error during synchronization:', error);
    
    // Update sync status with error
    await updateAutoSyncStatus({
      lastError: error instanceof Error ? error.message : 'Unknown sync error',
      nextSyncTime: Date.now() + AUTO_SYNC_INTERVAL
    });
    
    return false;
  }
}

// Start auto-sync
export async function startAutoSync(): Promise<void> {
  if (autoSyncInterval) {
    console.log('Auto-sync: Already running');
    return;
  }
  
  console.log('Auto-sync: Starting auto-sync service...');
  
  // Update status
  await updateAutoSyncStatus({
    isEnabled: true,
    nextSyncTime: Date.now() + AUTO_SYNC_INTERVAL
  });
  
  // Perform initial sync
  await performSync();
  
  // Set up recurring sync
  autoSyncInterval = setInterval(async () => {
    if (!isAutoSyncRunning) {
      isAutoSyncRunning = true;
      await performSync();
      isAutoSyncRunning = false;
    } else {
      console.log('Auto-sync: Skipping sync - previous sync still running');
    }
  }, AUTO_SYNC_INTERVAL);
  
  console.log(`Auto-sync: Service started with ${AUTO_SYNC_INTERVAL / 1000 / 60} minute interval`);
}

// Stop auto-sync
export async function stopAutoSync(): Promise<void> {
  if (autoSyncInterval) {
    clearInterval(autoSyncInterval);
    autoSyncInterval = null;
    isAutoSyncRunning = false;
    
    await updateAutoSyncStatus({
      isEnabled: false
    });
    
    console.log('Auto-sync: Service stopped');
  }
}

// Manual sync trigger
export async function triggerManualSync(): Promise<boolean> {
  console.log('Auto-sync: Manual sync triggered');
  return await performSync();
}

// Check if auto-sync is running
export function isAutoSyncActive(): boolean {
  return autoSyncInterval !== null;
}

// Get time until next sync
export async function getTimeUntilNextSync(): Promise<number> {
  const status = await getAutoSyncStatus();
  const now = Date.now();
  return Math.max(0, status.nextSyncTime - now);
}

// Format time remaining
export function formatTimeRemaining(milliseconds: number): string {
  const minutes = Math.floor(milliseconds / (1000 * 60));
  const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
  
  if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  }
  return `${seconds}s`;
}

// Auto-sync service initialization
export async function initializeAutoSync(): Promise<void> {
  try {
    const status = await getAutoSyncStatus();
    
    if (status.isEnabled) {
      await startAutoSync();
    }
    
    console.log('Auto-sync: Service initialized');
  } catch (error) {
    console.error('Auto-sync: Failed to initialize:', error);
  }
}

// Cleanup auto-sync on app close
export function cleanupAutoSync(): void {
  if (autoSyncInterval) {
    clearInterval(autoSyncInterval);
    autoSyncInterval = null;
    isAutoSyncRunning = false;
  }
}

// Auto-sync settings
export interface AutoSyncSettings {
  enabled: boolean;
  intervalMinutes: number;
}

// Get auto-sync settings
export async function getAutoSyncSettings(): Promise<AutoSyncSettings> {
  const status = await getAutoSyncStatus();
  return {
    enabled: status.isEnabled,
    intervalMinutes: AUTO_SYNC_INTERVAL / (1000 * 60)
  };
}

// Update auto-sync settings
export async function updateAutoSyncSettings(settings: AutoSyncSettings): Promise<void> {
  if (settings.enabled && !isAutoSyncActive()) {
    await startAutoSync();
  } else if (!settings.enabled && isAutoSyncActive()) {
    await stopAutoSync();
  }
  
  await updateAutoSyncStatus({
    isEnabled: settings.enabled
  });
}

// Export interval constant for external use
export { AUTO_SYNC_INTERVAL };
