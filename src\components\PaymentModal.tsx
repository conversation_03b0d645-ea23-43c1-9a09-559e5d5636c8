import React, { useState } from 'react';
import { CreditCardIcon, BanknoteIcon, WalletIcon } from 'lucide-react';
import { OrderItem, CustomerInfo, OrderType } from '../types';

interface PaymentModalProps {
  order: OrderItem[];
  customerInfo?: CustomerInfo;
  orderType: OrderType;
  discount?: number;
  discountType?: 'percentage' | 'fixed';
  onClose: () => void;
  onProcessPayment: (paymentDetails: any) => void;
}

export function PaymentModal({
  order,
  customerInfo,
  orderType,
  discount = 0,
  discountType = 'percentage',
  onClose,
  onProcessPayment
}: PaymentModalProps) {
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [splitPayment, setSplitPayment] = useState(false);
  const [splitAmounts, setSplitAmounts] = useState<Array<{method: string, amount: number}>>([]);

  const calculateSubtotal = () => {
    return order.reduce((total, item) => {
      const modifierPrice = item.modifierPrice || 0;
      return total + (item.price + modifierPrice) * item.quantity;
    }, 0);
  };

  const calculateDiscount = () => {
    const subtotal = calculateSubtotal();
    if (discountType === 'percentage') {
      return subtotal * (discount / 100);
    }
    return Math.min(discount, subtotal);
  };

  const calculateTaxableAmount = () => {
    return calculateSubtotal() - calculateDiscount();
  };

  const calculateTax = () => {
    return calculateTaxableAmount() * 0.0825; // 8.25% tax rate
  };

  const calculateTotal = () => {
    return calculateTaxableAmount() + calculateTax();
  };

  const total = calculateTotal();

  const addSplitPayment = () => {
    setSplitAmounts([...splitAmounts, {
      method: 'card',
      amount: 0
    }]);
  };

  const updateSplitAmount = (index: number, amount: string) => {
    const updated = [...splitAmounts];
    updated[index].amount = parseFloat(amount) || 0;
    setSplitAmounts(updated);
  };

  const updateSplitMethod = (index: number, method: string) => {
    const updated = [...splitAmounts];
    updated[index].method = method;
    setSplitAmounts(updated);
  };

  const removeSplitPayment = (index: number) => {
    setSplitAmounts(splitAmounts.filter((_, i) => i !== index));
  };

  const totalSplitAmount = splitAmounts.reduce((sum, item) => sum + item.amount, 0);
  const remainingAmount = total - totalSplitAmount;

  const handlePayment = () => {
    const paymentData = {
      order,
      customerInfo,
      orderType,
      subtotal: calculateSubtotal(),
      discount: calculateDiscount(),
      taxableAmount: calculateTaxableAmount(),
      tax: calculateTax(),
      total,
      timestamp: new Date().toISOString()
    };

    if (splitPayment) {
      onProcessPayment({
        ...paymentData,
        splitPayments: [...splitAmounts, {
          method: paymentMethod,
          amount: remainingAmount
        }]
      });
    } else {
      onProcessPayment({
        ...paymentData,
        method: paymentMethod
      });
    }
  };

  const orderTypeLabels = {
    'dine-in': 'Dine In',
    'takeout': 'Takeout',
    'delivery': 'Delivery'
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-lg p-6 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Payment</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700 text-2xl">
            ✕
          </button>
        </div>

        {/* Order Summary */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold mb-2">Order Summary</h3>
          <div className="text-sm space-y-1">
            <div className="flex justify-between">
              <span>Order Type:</span>
              <span className="font-medium">{orderTypeLabels[orderType]}</span>
            </div>
            {customerInfo?.name && (
              <div className="flex justify-between">
                <span>Customer:</span>
                <span className="font-medium">{customerInfo.name}</span>
              </div>
            )}
            {customerInfo?.phone && (
              <div className="flex justify-between">
                <span>Phone:</span>
                <span className="font-medium">{customerInfo.phone}</span>
              </div>
            )}
          </div>
        </div>

        {/* Financial Summary */}
        <div className="mb-6">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>${calculateSubtotal().toFixed(2)}</span>
            </div>
            {discount > 0 && (
              <div className="flex justify-between text-green-600">
                <span>
                  Discount ({discountType === 'percentage' ? `${discount}%` : `$${discount.toFixed(2)}`}):
                </span>
                <span>-${calculateDiscount().toFixed(2)}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span>Taxable Amount:</span>
              <span>${calculateTaxableAmount().toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Tax (8.25%):</span>
              <span>${calculateTax().toFixed(2)}</span>
            </div>
            <div className="flex justify-between font-bold text-lg border-t pt-2">
              <span>Total:</span>
              <span>${total.toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* Payment Options */}
        <div className="mb-6">
          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              id="split-payment"
              checked={splitPayment}
              onChange={() => setSplitPayment(!splitPayment)}
              className="mr-2"
            />
            <label htmlFor="split-payment" className="font-medium">Split Payment</label>
          </div>

          {splitPayment ? (
            <div className="space-y-4 mb-4">
              {splitAmounts.map((split, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <select
                    value={split.method}
                    onChange={e => updateSplitMethod(index, e.target.value)}
                    className="border rounded p-2 flex-1"
                  >
                    <option value="card">Card</option>
                    <option value="cash">Cash</option>
                    <option value="digital">Digital Wallet</option>
                  </select>
                  <input
                    type="number"
                    value={split.amount || ''}
                    onChange={e => updateSplitAmount(index, e.target.value)}
                    className="border rounded p-2 w-24"
                    placeholder="Amount"
                    step="0.01"
                    min="0"
                  />
                  <button
                    onClick={() => removeSplitPayment(index)}
                    className="text-red-500 hover:text-red-700 px-2"
                  >
                    ✕
                  </button>
                </div>
              ))}
              <button
                onClick={addSplitPayment}
                className="text-blue-600 hover:underline text-sm"
              >
                + Add Payment Method
              </button>
              <div className="flex justify-between font-medium mt-2 p-2 bg-gray-100 rounded">
                <span>Remaining:</span>
                <span>${remainingAmount.toFixed(2)}</span>
              </div>
              {remainingAmount > 0 && (
                <div className="mt-4">
                  <p className="mb-2 font-medium">
                    Select payment method for remaining amount:
                  </p>
                  <div className="grid grid-cols-3 gap-2">
                    <button
                      className={`p-3 border rounded-lg flex flex-col items-center transition-colors ${
                        paymentMethod === 'card' ? 'bg-blue-100 border-blue-500' : 'hover:bg-gray-50'
                      }`}
                      onClick={() => setPaymentMethod('card')}
                    >
                      <CreditCardIcon size={24} />
                      <span className="mt-1 text-sm">Card</span>
                    </button>
                    <button
                      className={`p-3 border rounded-lg flex flex-col items-center transition-colors ${
                        paymentMethod === 'cash' ? 'bg-blue-100 border-blue-500' : 'hover:bg-gray-50'
                      }`}
                      onClick={() => setPaymentMethod('cash')}
                    >
                      <BanknoteIcon size={24} />
                      <span className="mt-1 text-sm">Cash</span>
                    </button>
                    <button
                      className={`p-3 border rounded-lg flex flex-col items-center transition-colors ${
                        paymentMethod === 'digital' ? 'bg-blue-100 border-blue-500' : 'hover:bg-gray-50'
                      }`}
                      onClick={() => setPaymentMethod('digital')}
                    >
                      <WalletIcon size={24} />
                      <span className="mt-1 text-sm">Digital</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div>
              <p className="mb-2 font-medium">Select payment method:</p>
              <div className="grid grid-cols-3 gap-2">
                <button
                  className={`p-3 border rounded-lg flex flex-col items-center transition-colors ${
                    paymentMethod === 'card' ? 'bg-blue-100 border-blue-500' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setPaymentMethod('card')}
                >
                  <CreditCardIcon size={24} />
                  <span className="mt-1 text-sm">Card</span>
                </button>
                <button
                  className={`p-3 border rounded-lg flex flex-col items-center transition-colors ${
                    paymentMethod === 'cash' ? 'bg-blue-100 border-blue-500' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setPaymentMethod('cash')}
                >
                  <BanknoteIcon size={24} />
                  <span className="mt-1 text-sm">Cash</span>
                </button>
                <button
                  className={`p-3 border rounded-lg flex flex-col items-center transition-colors ${
                    paymentMethod === 'digital' ? 'bg-blue-100 border-blue-500' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setPaymentMethod('digital')}
                >
                  <WalletIcon size={24} />
                  <span className="mt-1 text-sm">Digital</span>
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handlePayment}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            disabled={splitPayment && remainingAmount < 0}
          >
            Complete Payment
          </button>
        </div>
      </div>
    </div>
  );
}