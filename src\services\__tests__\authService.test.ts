// Mock JSEncrypt for testing
jest.mock('jsencrypt', () => ({
  JSEncrypt: jest.fn().mockImplementation(() => ({
    setPublicKey: jest.fn(),
    encrypt: jest.fn().mockReturnValue('encrypted-password')
  }))
}));

// Mock fetch
global.fetch = jest.fn();

import { login, signIn, appUser } from '../authService';

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset appUser state
    appUser.UserId = 0;
    appUser.Pass = '';
    appUser.UserName = '';
    appUser.Token = '';
    appUser.DbName = '';
  });

  describe('login', () => {
    it('should return success with databases when login is successful', async () => {
      const mockResponse = {
        success: true,
        data: {
          isLogged: false,
          databases: [
            { id: 1, name: 'Test DB', source: 'test_db' },
            { id: 2, name: 'Another DB', source: 'another_db' }
          ]
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const result = await login('testuser', 'testpass');

      expect(result).toEqual({
        success: true,
        isLogged: false,
        dbList: [
          { id: 1, name: 'Test DB', source: 'test_db' },
          { id: 2, name: 'Another DB', source: 'another_db' }
        ]
      });
    });

    it('should return failure when login fails', async () => {
      const mockResponse = {
        success: false,
        data: {
          isLogged: false,
          databases: []
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        json: async () => mockResponse
      });

      const result = await login('testuser', 'wrongpass');

      expect(result).toEqual({
        success: false,
        isLogged: false,
        dbList: []
      });
    });
  });

  describe('signIn', () => {
    beforeEach(() => {
      appUser.DbName = 'test_db';
    });

    it('should return success when token generation is successful', async () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyaWQiOiIxMjMifQ.mock-signature';
      const mockResponse = {
        success: true,
        data: mockToken
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const result = await signIn('testuser', 'testpass');

      expect(result.isOk).toBe(true);
      expect(result.data).toBeDefined();
      expect(appUser.Token).toBe(mockToken);
      expect(appUser.UserName).toBe('testuser');
    });

    it('should return failure when token generation fails', async () => {
      const mockResponse = {
        success: false,
        data: ''
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        json: async () => mockResponse
      });

      const result = await signIn('testuser', 'testpass');

      expect(result.isOk).toBe(false);
      expect(result.data).toBeUndefined();
    });
  });
});
