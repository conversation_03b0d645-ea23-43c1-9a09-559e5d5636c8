import React, { useEffect, useState } from 'react';
import { ProductGrid } from '../components/ProductGrid';
import { CategoryNav } from '../components/CategoryNav';
import { SearchBar } from '../components/SearchBar';
import { OrderSummary } from '../components/OrderSummary';
import { PaymentModal } from '../components/PaymentModal';
import { ModifierModal } from '../components/ModifierModal';
import { ReceiptModal } from '../components/ReceiptModal';
import { Product, OrderItem, Modifier, CustomerInfo, OrderType, Category, Currency, Option } from '../types';
import {
  getEnrichedProducts,
  filterProductsByCategory,
  searchProducts,
  isAuthenticated,
  formatPrice,
  refreshAllData,
  getLastSyncTime
} from '../services/itemService';
import { EnrichedProduct } from '../services/itemService';

export function PosPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [currentOrder, setCurrentOrder] = useState<OrderItem[]>([]);
  const [showPaymentModal, setShowPaymentModal] = useState<boolean>(false);
  const [showModifierModal, setShowModifierModal] = useState<boolean>(false);
  const [showReceiptModal, setShowReceiptModal] = useState<boolean>(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [products, setProducts] = useState<EnrichedProduct[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [options, setOptions] = useState<Option[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<EnrichedProduct[]>([]);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({ name: '', phone: '' });
  const [orderType, setOrderType] = useState<OrderType>('dine-in');
  const [discount, setDiscount] = useState<number>(0);
  const [discountType, setDiscountType] = useState<'percentage' | 'fixed'>('percentage');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [lastSync, setLastSync] = useState<number>(0);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      if (!isAuthenticated()) {
        setError('User not authenticated. Please log in first.');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError('');

        // Load enriched products with units, prices, and families
        const { products: enrichedProducts, categories: categoriesData, currencies: currenciesData, options: optionsData } = await getEnrichedProducts();

        // Get last sync time
        const syncTime = await getLastSyncTime();

        setProducts(enrichedProducts);
        setCategories(categoriesData);
        setCurrencies(currenciesData);
        setOptions(optionsData);
        setFilteredProducts(enrichedProducts);
        setLastSync(syncTime);
      } catch (error) {
        console.error('Error loading data:', error);
        setError('Failed to load products. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Filter products based on category and search
  useEffect(() => {
    let filtered = filterProductsByCategory(products, selectedCategory);
    filtered = searchProducts(filtered, searchQuery);
    setFilteredProducts(filtered);
  }, [products, selectedCategory, searchQuery]);

  // Refresh data function
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshAllData();
      // Reload data after refresh
      const { products: enrichedProducts, categories: categoriesData, currencies: currenciesData, options: optionsData } = await getEnrichedProducts();
      const syncTime = await getLastSyncTime();

      setProducts(enrichedProducts);
      setCategories(categoriesData);
      setCurrencies(currenciesData);
      setOptions(optionsData);
      setFilteredProducts(enrichedProducts);
      setLastSync(syncTime);
    } catch (error) {
      console.error('Error refreshing data:', error);
      setError('Failed to refresh data. Please try again.');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Add product to order
  const addToOrder = (product: Product | EnrichedProduct) => {
    // Skip if product is deactivated or deleted
    if (product.deactivated || product.isDeleted) {
      alert('This product is not available');
      return;
    }

    if (product.hasModifiers) {
      setSelectedProduct(product);
      setShowModifierModal(true);
    } else {
      const existingItem = currentOrder.find(item => item.id === product.id && !item.modifiers?.length);
      if (existingItem) {
        setCurrentOrder(currentOrder.map(item => item.id === product.id && !item.modifiers?.length ? {
          ...item,
          quantity: item.quantity + 1
        } : item));
      } else {
        setCurrentOrder([...currentOrder, {
          ...product,
          quantity: 1,
          // Ensure required properties for OrderItem
          price: product.price || 0,
          category: product.category || 'general',
          hasModifiers: product.hasModifiers || false
        }]);
      }
    }
  };

  // Add product with modifiers to order
  const addToOrderWithModifiers = (product: Product, selectedModifiers: Modifier[]) => {
    setCurrentOrder([...currentOrder, {
      ...product,
      quantity: 1,
      modifiers: selectedModifiers,
      modifierPrice: selectedModifiers.reduce((total, mod) => total + mod.price, 0)
    }]);
    setShowModifierModal(false);
  };

  // Update item quantity
  const updateQuantity = (index: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromOrder(index);
      return;
    }
    const updatedOrder = [...currentOrder];
    updatedOrder[index].quantity = newQuantity;
    setCurrentOrder(updatedOrder);
  };

  // Remove item from order
  const removeFromOrder = (index: number) => {
    setCurrentOrder(currentOrder.filter((_, i) => i !== index));
  };

  // Process payment
  const processPayment = (paymentDetails: any) => {
    console.log('Processing payment:', paymentDetails);
    setShowPaymentModal(false);
    setShowReceiptModal(true);
  };

  // Clear current order
  const clearOrder = () => {
    setCurrentOrder([]);
    setShowReceiptModal(false);
  };

  if (loading) {
    return (
      <div className="flex flex-1 items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading products...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-1 items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-lg font-semibold">Error Loading Products</p>
            <p className="text-sm text-gray-600 mt-1">{error}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 overflow-hidden">
      {/* Left Side - Product Selection */}
      <div className="w-2/3 flex flex-col bg-white shadow-md">
        {/* Search and Categories */}
        <div className="p-4 border-b">
          <div className="flex justify-between items-center mb-4">
            <div className="flex-1">
              <SearchBar onSearch={setSearchQuery} />
            </div>
            <div className="ml-4 flex items-center space-x-2">
              {lastSync > 0 && (
                <span className="text-xs text-gray-500">
                  Last sync: {new Date(lastSync).toLocaleTimeString()}
                </span>
              )}
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
              >
                {isRefreshing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                    Syncing...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Refresh
                  </>
                )}
              </button>
            </div>
          </div>
          <CategoryNav categories={categories} selectedCategory={selectedCategory} onSelectCategory={setSelectedCategory} />
        </div>
        {/* Product Grid */}
        <div className="flex-1 overflow-y-auto p-4">
          <h2 className="text-xl font-bold mb-4">Products</h2>
          {filteredProducts.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              <p>No products found</p>
              {searchQuery && <p className="text-sm">Try adjusting your search terms</p>}
            </div>
          ) : (
            <ProductGrid products={filteredProducts} onProductSelect={addToOrder} />
          )}
        </div>
      </div>
      {/* Right Side - Order Summary */}
      <div className="w-1/3 bg-gray-50 border-l border-gray-200">
        <OrderSummary
          orderItems={currentOrder}
          updateQuantity={updateQuantity}
          removeItem={removeFromOrder}
          onCheckout={() => setShowPaymentModal(true)}
          onClearOrder={clearOrder}
          customerInfo={customerInfo}
          orderType={orderType}
          discount={discount}
          discountType={discountType}
          onCustomerInfoChange={setCustomerInfo}
          onOrderTypeChange={setOrderType}
          onDiscountChange={(newDiscount, newDiscountType) => {
            setDiscount(newDiscount);
            setDiscountType(newDiscountType);
          }}
        />
      </div>
      {/* Modals */}
      {showModifierModal && selectedProduct && <ModifierModal product={selectedProduct} onClose={() => setShowModifierModal(false)} onAddToOrder={addToOrderWithModifiers} />}
      {showPaymentModal && (
        <PaymentModal
          order={currentOrder}
          customerInfo={customerInfo}
          orderType={orderType}
          discount={discount}
          discountType={discountType}
          onClose={() => setShowPaymentModal(false)}
          onProcessPayment={processPayment}
        />
      )}
      {showReceiptModal && <ReceiptModal order={currentOrder} onClose={clearOrder} />}
    </div>
  );
}