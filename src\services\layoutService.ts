import { dbService } from './indexedDBService';
import { TableLayout, LayoutTable } from '../types';

// Initialize layout service
export async function initializeLayoutService(): Promise<void> {
  await dbService.init();
}

// Get all layouts
export async function getAllLayouts(): Promise<TableLayout[]> {
  await initializeLayoutService();
  return await dbService.getAll<TableLayout>('tableLayouts');
}

// Get layout by ID
export async function getLayoutById(layoutId: string): Promise<TableLayout | null> {
  await initializeLayoutService();
  return await dbService.get<TableLayout>('tableLayouts', layoutId);
}

// Get active layout
export async function getActiveLayout(): Promise<TableLayout | null> {
  await initializeLayoutService();
  const activeLayouts = await dbService.getByIndex<TableLayout>('tableLayouts', 'isActive', true);
  return activeLayouts.length > 0 ? activeLayouts[0] : null;
}

// Save layout
export async function saveLayout(layout: TableLayout): Promise<void> {
  await initializeLayoutService();
  await dbService.put('tableLayouts', layout);
}

// Create new layout
export async function createLayout(
  name: string, 
  tables: LayoutTable[], 
  floorPlanImage: string | null = null
): Promise<TableLayout> {
  const layout: TableLayout = {
    id: Date.now().toString(),
    name,
    tables,
    floorPlanImage,
    isActive: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  await saveLayout(layout);
  return layout;
}

// Update layout
export async function updateLayout(
  layoutId: string, 
  updates: Partial<Omit<TableLayout, 'id' | 'createdAt'>>
): Promise<void> {
  await initializeLayoutService();
  
  const existingLayout = await getLayoutById(layoutId);
  if (!existingLayout) {
    throw new Error('Layout not found');
  }
  
  const updatedLayout: TableLayout = {
    ...existingLayout,
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  await saveLayout(updatedLayout);
}

// Delete layout
export async function deleteLayout(layoutId: string): Promise<void> {
  await initializeLayoutService();
  
  const layout = await getLayoutById(layoutId);
  if (!layout) {
    throw new Error('Layout not found');
  }
  
  if (layout.isActive) {
    throw new Error('Cannot delete active layout');
  }
  
  await dbService.delete('tableLayouts', layoutId);
}

// Set active layout
export async function setActiveLayout(layoutId: string): Promise<void> {
  await initializeLayoutService();
  
  // First, set all layouts to inactive
  const allLayouts = await getAllLayouts();
  for (const layout of allLayouts) {
    if (layout.isActive) {
      await updateLayout(layout.id, { isActive: false });
    }
  }
  
  // Then set the specified layout as active
  await updateLayout(layoutId, { isActive: true });
}

// Duplicate layout
export async function duplicateLayout(layoutId: string, newName: string): Promise<TableLayout> {
  await initializeLayoutService();
  
  const originalLayout = await getLayoutById(layoutId);
  if (!originalLayout) {
    throw new Error('Layout not found');
  }
  
  return await createLayout(newName, [...originalLayout.tables], originalLayout.floorPlanImage);
}

// Get layouts sorted by creation date
export async function getLayoutsSortedByDate(): Promise<TableLayout[]> {
  const layouts = await getAllLayouts();
  return layouts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
}

// Search layouts by name
export async function searchLayoutsByName(searchTerm: string): Promise<TableLayout[]> {
  const layouts = await getAllLayouts();
  return layouts.filter(layout => 
    layout.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
}

// Get layout statistics
export async function getLayoutStatistics(): Promise<{
  totalLayouts: number;
  activeLayout: string | null;
  totalTables: number;
  averageTablesPerLayout: number;
}> {
  const layouts = await getAllLayouts();
  const activeLayout = await getActiveLayout();
  const totalTables = layouts.reduce((sum, layout) => sum + layout.tables.length, 0);
  
  return {
    totalLayouts: layouts.length,
    activeLayout: activeLayout?.name || null,
    totalTables,
    averageTablesPerLayout: layouts.length > 0 ? totalTables / layouts.length : 0
  };
}

// Validate layout
export function validateLayout(layout: Partial<TableLayout>): string[] {
  const errors: string[] = [];
  
  if (!layout.name || layout.name.trim().length === 0) {
    errors.push('Layout name is required');
  }
  
  if (!layout.tables || layout.tables.length === 0) {
    errors.push('Layout must have at least one table');
  }
  
  if (layout.tables) {
    const tableNumbers = layout.tables.map(t => t.number);
    const duplicateNumbers = tableNumbers.filter((num, index) => tableNumbers.indexOf(num) !== index);
    if (duplicateNumbers.length > 0) {
      errors.push(`Duplicate table numbers found: ${duplicateNumbers.join(', ')}`);
    }
  }
  
  return errors;
}

// Export layout data (for backup/sharing)
export async function exportLayout(layoutId: string): Promise<string> {
  const layout = await getLayoutById(layoutId);
  if (!layout) {
    throw new Error('Layout not found');
  }
  
  return JSON.stringify(layout, null, 2);
}

// Import layout data
export async function importLayout(layoutData: string, newName?: string): Promise<TableLayout> {
  try {
    const parsedLayout = JSON.parse(layoutData) as TableLayout;
    
    // Validate the imported layout
    const errors = validateLayout(parsedLayout);
    if (errors.length > 0) {
      throw new Error(`Invalid layout data: ${errors.join(', ')}`);
    }
    
    // Create new layout with imported data
    return await createLayout(
      newName || `${parsedLayout.name} (Imported)`,
      parsedLayout.tables,
      parsedLayout.floorPlanImage
    );
  } catch (error) {
    throw new Error(`Failed to import layout: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Initialize default layout if none exists
export async function initializeDefaultLayout(): Promise<void> {
  await initializeLayoutService();
  
  const existingLayouts = await getAllLayouts();
  if (existingLayouts.length === 0) {
    const defaultTables: LayoutTable[] = [
      { id: 1, x: 100, y: 100, width: 80, height: 80, number: 1, seats: 4, status: 'available', borderColor: '#16a34a' },
      { id: 2, x: 200, y: 100, width: 80, height: 80, number: 2, seats: 4, status: 'available', borderColor: '#16a34a' },
      { id: 3, x: 300, y: 100, width: 80, height: 80, number: 3, seats: 4, status: 'available', borderColor: '#16a34a' },
      { id: 4, x: 400, y: 100, width: 80, height: 80, number: 4, seats: 4, status: 'available', borderColor: '#16a34a' },
      { id: 5, x: 100, y: 200, width: 120, height: 80, number: 5, seats: 6, status: 'available', borderColor: '#16a34a' },
      { id: 6, x: 250, y: 200, width: 120, height: 80, number: 6, seats: 6, status: 'available', borderColor: '#16a34a' },
    ];
    
    const defaultLayout = await createLayout('Default Layout', defaultTables);
    await setActiveLayout(defaultLayout.id);
  }
}
