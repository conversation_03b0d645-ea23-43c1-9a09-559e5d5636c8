// Mock IndexedDB service
jest.mock('../indexedDBService', () => ({
  dbService: {
    init: jest.fn(),
    put: jest.fn(),
    getAll: jest.fn(),
    get: jest.fn(),
    getByIndex: jest.fn()
  }
}));

// Mock auth service
jest.mock('../authService', () => ({
  appUser: {
    UserName: 'testuser'
  }
}));

import { 
  checkout, 
  calculateOrderTotals, 
  getAllOrders, 
  updateOrderStatus,
  getOrderStatistics 
} from '../orderService';
import { dbService } from '../indexedDBService';
import { OrderItem, CustomerInfo } from '../../types';

describe('OrderService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('calculateOrderTotals', () => {
    const mockItems: OrderItem[] = [
      {
        id: 1,
        name: 'Burger',
        price: 10.00,
        quantity: 2,
        category: 'food',
        hasModifiers: false
      },
      {
        id: 2,
        name: 'Fries',
        price: 5.00,
        quantity: 1,
        category: 'food',
        hasModifiers: false
      }
    ];

    it('should calculate totals with percentage discount', () => {
      const result = calculateOrderTotals(mockItems, 10, 'percentage', 0.08);
      
      expect(result.subtotal).toBe(25.00); // (10*2) + (5*1)
      expect(result.discount).toBe(2.50); // 10% of 25
      expect(result.taxableAmount).toBe(22.50); // 25 - 2.5
      expect(result.tax).toBe(1.80); // 8% of 22.5
      expect(result.total).toBe(24.30); // 22.5 + 1.8
    });

    it('should calculate totals with fixed discount', () => {
      const result = calculateOrderTotals(mockItems, 5, 'fixed', 0.08);
      
      expect(result.subtotal).toBe(25.00);
      expect(result.discount).toBe(5.00);
      expect(result.taxableAmount).toBe(20.00);
      expect(result.tax).toBe(1.60);
      expect(result.total).toBe(21.60);
    });

    it('should handle modifier prices', () => {
      const itemsWithModifiers: OrderItem[] = [
        {
          id: 1,
          name: 'Burger',
          price: 10.00,
          quantity: 1,
          category: 'food',
          hasModifiers: true,
          modifierPrice: 2.00
        }
      ];

      const result = calculateOrderTotals(itemsWithModifiers, 0, 'percentage', 0.08);
      
      expect(result.subtotal).toBe(12.00); // 10 + 2
      expect(result.total).toBe(12.96); // 12 + (12 * 0.08)
    });
  });

  describe('checkout', () => {
    const mockItems: OrderItem[] = [
      {
        id: 1,
        name: 'Test Item',
        price: 10.00,
        quantity: 1,
        category: 'food',
        hasModifiers: false
      }
    ];

    const mockCustomerInfo: CustomerInfo = {
      name: 'John Doe',
      phone: '************'
    };

    it('should create order, invoice, and receipt on successful checkout', async () => {
      (dbService.init as jest.Mock).mockResolvedValue(undefined);
      (dbService.put as jest.Mock).mockResolvedValue(undefined);

      const result = await checkout(
        mockItems,
        mockCustomerInfo,
        'dine-in',
        0,
        'percentage',
        'cash'
      );

      expect(result.order).toBeDefined();
      expect(result.invoice).toBeDefined();
      expect(result.receipt).toBeDefined();

      expect(result.order.orderNumber).toMatch(/^ORD-\d{8}-\d{4}$/);
      expect(result.invoice.invoiceNumber).toMatch(/^INV-\d{8}-\d{4}$/);
      expect(result.receipt.receiptNumber).toMatch(/^RCP-\d{8}-\d{4}$/);

      expect(result.order.customerInfo).toEqual(mockCustomerInfo);
      expect(result.order.items).toEqual(mockItems);
      expect(result.order.status).toBe('pending');

      // Verify database calls
      expect(dbService.put).toHaveBeenCalledTimes(3);
      expect(dbService.put).toHaveBeenCalledWith('orders', expect.any(Object));
      expect(dbService.put).toHaveBeenCalledWith('invoices', expect.any(Object));
      expect(dbService.put).toHaveBeenCalledWith('receipts', expect.any(Object));
    });

    it('should throw error for empty order', async () => {
      await expect(checkout([], mockCustomerInfo, 'dine-in')).rejects.toThrow('Cannot checkout with empty order');
    });

    it('should handle database errors', async () => {
      (dbService.init as jest.Mock).mockResolvedValue(undefined);
      (dbService.put as jest.Mock).mockRejectedValue(new Error('Database error'));

      await expect(checkout(mockItems, mockCustomerInfo, 'dine-in')).rejects.toThrow('Failed to save order data');
    });
  });

  describe('getAllOrders', () => {
    it('should return orders sorted by newest first', async () => {
      const mockOrders = [
        { id: '1', createdAt: 1000, orderNumber: 'ORD-001' },
        { id: '2', createdAt: 2000, orderNumber: 'ORD-002' },
        { id: '3', createdAt: 1500, orderNumber: 'ORD-003' }
      ];

      (dbService.init as jest.Mock).mockResolvedValue(undefined);
      (dbService.getAll as jest.Mock).mockResolvedValue(mockOrders);

      const result = await getAllOrders();

      expect(result).toEqual([
        { id: '2', createdAt: 2000, orderNumber: 'ORD-002' },
        { id: '3', createdAt: 1500, orderNumber: 'ORD-003' },
        { id: '1', createdAt: 1000, orderNumber: 'ORD-001' }
      ]);
    });
  });

  describe('updateOrderStatus', () => {
    it('should update order and invoice status', async () => {
      const mockOrder = {
        id: 'order1',
        invoiceId: 'invoice1',
        status: 'pending',
        updatedAt: 1000
      };

      const mockInvoice = {
        id: 'invoice1',
        status: 'pending',
        updatedAt: 1000
      };

      (dbService.init as jest.Mock).mockResolvedValue(undefined);
      (dbService.get as jest.Mock)
        .mockResolvedValueOnce(mockOrder)
        .mockResolvedValueOnce(mockInvoice);
      (dbService.put as jest.Mock).mockResolvedValue(undefined);

      await updateOrderStatus('order1', 'completed');

      expect(dbService.put).toHaveBeenCalledWith('orders', expect.objectContaining({
        status: 'completed',
        updatedAt: expect.any(Number)
      }));

      expect(dbService.put).toHaveBeenCalledWith('invoices', expect.objectContaining({
        status: 'completed',
        updatedAt: expect.any(Number)
      }));
    });

    it('should throw error if order not found', async () => {
      (dbService.init as jest.Mock).mockResolvedValue(undefined);
      (dbService.get as jest.Mock).mockResolvedValue(null);

      await expect(updateOrderStatus('nonexistent', 'completed')).rejects.toThrow('Order not found');
    });
  });

  describe('getOrderStatistics', () => {
    it('should calculate order statistics correctly', async () => {
      const today = new Date().toLocaleDateString();
      const mockOrders = [
        { status: 'pending', total: 25.00, date: today },
        { status: 'completed', total: 30.00, date: today },
        { status: 'completed', total: 15.00, date: '1/1/2023' },
        { status: 'pending', total: 20.00, date: '1/1/2023' }
      ];

      (dbService.init as jest.Mock).mockResolvedValue(undefined);
      (dbService.getAll as jest.Mock).mockResolvedValue(mockOrders);

      const result = await getOrderStatistics();

      expect(result.totalOrders).toBe(4);
      expect(result.pendingOrders).toBe(2);
      expect(result.completedOrders).toBe(2);
      expect(result.totalRevenue).toBe(90.00);
      expect(result.todayOrders).toBe(2);
      expect(result.todayRevenue).toBe(55.00);
    });
  });
});
