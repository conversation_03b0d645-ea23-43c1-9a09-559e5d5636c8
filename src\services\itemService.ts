import { appUser } from './authService';
import { Product, Category, Family } from '../types';

// API Response interfaces
export interface ItemListResponse {
  success: boolean;
  methodName: string;
  errorMessage: string;
  data: Product[];
}

export interface FamilyListResponse {
  success: boolean;
  methodName: string;
  errorMessage: string;
  data: Family[];
}

// Get item list from API
export async function getItemList(stmp: number = 0): Promise<Product[]> {
  try {
    const response = await fetch(
      `${appUser.apiURL}/api/Item/GetItemListAsync?stmp=${stmp}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${appUser.identityToken}`,
          'ClientKey': appUser.clientKey,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: ItemListResponse = await response.json();
    
    if (result.success) {
      // Transform API data to include UI-compatible properties
      return result.data.map(item => ({
        ...item,
        price: 0, // You might want to get price from another API endpoint
        category: item.familyId.toString(), // Map familyId to category for compatibility
        hasModifiers: false // You might want to determine this based on item properties
      }));
    } else {
      throw new Error(result.errorMessage || 'Failed to fetch items');
    }
  } catch (error) {
    console.error('Error fetching items:', error);
    throw error;
  }
}

// Get family list from API
export async function getFamilyList(stmp: number = 0): Promise<Family[]> {
  try {
    const response = await fetch(
      `${appUser.apiURL}/api/Family/GetFamilyListAsync?stmp=${stmp}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${appUser.identityToken}`,
          'ClientKey': appUser.clientKey,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: FamilyListResponse = await response.json();

    if (result.success) {
      // Filter out deleted families
      return result.data.filter(family => !family.isDeleted);
    } else {
      throw new Error(result.errorMessage || 'Failed to fetch families');
    }
  } catch (error) {
    console.error('Error fetching families:', error);
    throw error;
  }
}

// Get products with family information
export async function getProductsWithFamilies(): Promise<{ products: Product[], families: Family[], categories: Category[] }> {
  try {
    const [products, families] = await Promise.all([
      getItemList(),
      getFamilyList()
    ]);

    // Create a family lookup map
    const familyMap = new Map(families.map(family => [family.id, family]));

    // Enrich products with family information
    const enrichedProducts = products.map(product => ({
      ...product,
      familyName: familyMap.get(product.familyId)?.name || 'Unknown Family'
    }));

    // Create categories from families
    const categories: Category[] = [
      { id: 'all', name: 'All' },
      ...families.map(family => ({
        id: family.id.toString(),
        name: family.name
      }))
    ];

    return {
      products: enrichedProducts,
      families,
      categories
    };
  } catch (error) {
    console.error('Error fetching products with families:', error);
    throw error;
  }
}

// Get categories based on families from API
export async function getCategories(): Promise<Category[]> {
  try {
    const families = await getFamilyList();

    const categories: Category[] = [
      { id: 'all', name: 'All' },
      ...families.map(family => ({
        id: family.id.toString(),
        name: family.name
      }))
    ];

    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    // Return default categories on error
    return [
      { id: 'all', name: 'All' },
      { id: '1', name: 'General' }
    ];
  }
}

// Filter products by category (familyId)
export function filterProductsByCategory(products: Product[], categoryId: string): Product[] {
  if (categoryId === 'all') {
    return products;
  }
  
  return products.filter(product => product.familyId.toString() === categoryId);
}

// Search products by name
export function searchProducts(products: Product[], query: string): Product[] {
  if (!query.trim()) {
    return products;
  }
  
  const lowercaseQuery = query.toLowerCase();
  return products.filter(product => 
    product.name.toLowerCase().includes(lowercaseQuery) ||
    product.abbreviation.toLowerCase().includes(lowercaseQuery)
  );
}

// Check if user is authenticated for API calls
export function isAuthenticated(): boolean {
  return !!(appUser.identityToken && appUser.clientKey);
}

// Get item image as data URL (since API returns base64)
export function getItemImageUrl(item: Product): string {
  if (item.image && item.image !== '') {
    // Check if it's already a data URL
    if (item.image.startsWith('data:')) {
      return item.image;
    }
    // Assume it's base64 and create data URL
    return `data:image/png;base64,${item.image}`;
  }
  
  // Return placeholder image URL
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIiBmaWxsPSIjOTk5Ij5ObyBJbWFnZTwvdGV4dD48L3N2Zz4=';
}
